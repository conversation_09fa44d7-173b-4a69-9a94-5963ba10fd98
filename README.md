# PDF文本修改工具

这是一个用于修改PDF文件中文本内容的Python工具集，特别适用于修改发票模板等文档。

## 功能特性

- 📖 读取PDF文件内容
- 🔍 查找特定文本在PDF中的位置
- ✏️ 单个文本替换
- 🔄 批量文本替换
- 📝 支持中文文本
- ⚙️ 配置文件驱动的批量操作

## 安装依赖

1. 激活虚拟环境：
```bash
source venv/bin/activate
```

2. 安装依赖包：
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 交互式单个文本修改

运行 `pdf_modifier.py` 进行交互式文本修改：

```bash
python pdf_modifier.py
```

功能：
- 显示PDF所有页面的文本内容
- 查找特定文本的位置
- 替换单个文本
- 保存修改后的PDF

### 2. 批量文本替换

运行 `batch_pdf_modifier.py` 进行批量文本替换：

```bash
python batch_pdf_modifier.py
```

首次运行会创建示例配置文件 `replacement_config.json`，编辑此文件设置替换规则：

```json
{
  "name": "张三",
  "email": "<EMAIL>",
  "Jun 19, 2025": "2025年6月19日",
  "$20.00": "¥150.00",
  "Cursor Pro": "Cursor专业版",
  "2F55C079՞0002": "INV-2025-001"
}
```

## 文件说明

- `pdf_modifier.py` - 交互式PDF文本修改工具
- `batch_pdf_modifier.py` - 批量PDF文本替换工具
- `replacement_config.json` - 批量替换配置文件
- `template_cursor.pdf` - 原始PDF模板
- `modified_*.pdf` - 修改后的PDF文件

## 注意事项

1. **字体问题**：替换的文本可能与原文本字体不同
2. **位置精确性**：文本替换基于原文本的精确位置
3. **备份**：建议在修改前备份原始PDF文件
4. **中文支持**：工具支持中文文本的替换

## 示例用法

### 修改发票信息

1. 编辑 `replacement_config.json`：
```json
{
  "name": "李四",
  "email": "<EMAIL>",
  "Jun 19, 2025": "2025年7月1日",
  "$20.00": "¥200.00"
}
```

2. 运行批量替换：
```bash
python batch_pdf_modifier.py
```

3. 确认替换操作，生成新的PDF文件

## 技术实现

- 使用 `PyMuPDF (fitz)` 库进行PDF操作
- 支持文本查找、删除和插入
- 使用JSON配置文件管理替换规则
- 自动生成带时间戳的输出文件名

## 故障排除

如果遇到问题：
1. 确保PDF文件存在且可读
2. 检查要替换的文本是否完全匹配（区分大小写）
3. 确保虚拟环境已激活
4. 检查依赖包是否正确安装
