#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本修改工具
用于读取和修改PDF文件中的文本内容
"""

import fitz  # PyMuPDF
import sys
import os
from typing import Dict, List, Tuple


class PDFModifier:
    def __init__(self, pdf_path: str):
        """
        初始化PDF修改器
        
        Args:
            pdf_path: PDF文件路径
        """
        self.pdf_path = pdf_path
        self.doc = None
        
    def open_pdf(self):
        """打开PDF文件"""
        try:
            self.doc = fitz.open(self.pdf_path)
            print(f"成功打开PDF文件: {self.pdf_path}")
            print(f"页数: {len(self.doc)}")
            return True
        except Exception as e:
            print(f"打开PDF文件失败: {e}")
            return False
    
    def close_pdf(self):
        """关闭PDF文件"""
        if self.doc:
            self.doc.close()
    
    def read_text(self) -> Dict[int, str]:
        """
        读取PDF中的所有文本
        
        Returns:
            字典，键为页码，值为该页的文本内容
        """
        if not self.doc:
            print("请先打开PDF文件")
            return {}
        
        text_content = {}
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            text = page.get_text()
            text_content[page_num + 1] = text
            print(f"\n=== 第 {page_num + 1} 页内容 ===")
            print(text)
            print("=" * 50)
        
        return text_content
    
    def find_text_instances(self, search_text: str) -> List[Tuple[int, fitz.Rect]]:
        """
        查找文本在PDF中的位置
        
        Args:
            search_text: 要查找的文本
            
        Returns:
            列表，包含(页码, 矩形区域)的元组
        """
        if not self.doc:
            print("请先打开PDF文件")
            return []
        
        instances = []
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            text_instances = page.search_for(search_text)
            for rect in text_instances:
                instances.append((page_num + 1, rect))
                print(f"在第 {page_num + 1} 页找到文本 '{search_text}': {rect}")
        
        return instances
    
    def replace_text(self, old_text: str, new_text: str) -> bool:
        """
        替换PDF中的文本
        
        Args:
            old_text: 要替换的原文本
            new_text: 新文本
            
        Returns:
            是否成功替换
        """
        if not self.doc:
            print("请先打开PDF文件")
            return False
        
        replaced_count = 0
        
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            
            # 查找要替换的文本
            text_instances = page.search_for(old_text)
            
            for rect in text_instances:
                # 删除原文本区域
                page.add_redact_annot(rect)
                page.apply_redactions()
                
                # 在相同位置插入新文本
                page.insert_text(rect.tl, new_text, fontsize=12, color=(0, 0, 0))
                replaced_count += 1
                print(f"在第 {page_num + 1} 页替换了文本: '{old_text}' -> '{new_text}'")
        
        if replaced_count > 0:
            print(f"总共替换了 {replaced_count} 处文本")
            return True
        else:
            print(f"未找到要替换的文本: '{old_text}'")
            return False
    
    def save_pdf(self, output_path: str = None):
        """
        保存修改后的PDF
        
        Args:
            output_path: 输出文件路径，如果为None则覆盖原文件
        """
        if not self.doc:
            print("请先打开PDF文件")
            return False
        
        if output_path is None:
            output_path = self.pdf_path
        
        try:
            self.doc.save(output_path)
            print(f"PDF已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存PDF失败: {e}")
            return False


def main():
    """主函数"""
    pdf_file = "template_cursor.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"PDF文件不存在: {pdf_file}")
        return
    
    # 创建PDF修改器实例
    modifier = PDFModifier(pdf_file)
    
    # 打开PDF文件
    if not modifier.open_pdf():
        return
    
    try:
        # 读取PDF内容
        print("正在读取PDF内容...")
        text_content = modifier.read_text()
        
        # 示例：查找特定文本
        print("\n正在查找文本...")
        search_text = input("请输入要查找的文本（直接回车跳过）: ").strip()
        if search_text:
            instances = modifier.find_text_instances(search_text)
            if instances:
                print(f"找到 {len(instances)} 个匹配项")
            else:
                print("未找到匹配的文本")
        
        # 示例：替换文本
        print("\n文本替换功能:")
        old_text = input("请输入要替换的原文本（直接回车跳过）: ").strip()
        if old_text:
            new_text = input("请输入新文本: ").strip()
            if modifier.replace_text(old_text, new_text):
                # 保存修改后的文件
                output_file = f"modified_{pdf_file}"
                modifier.save_pdf(output_file)
                print(f"修改后的PDF已保存为: {output_file}")
    
    finally:
        # 关闭PDF文件
        modifier.close_pdf()


if __name__ == "__main__":
    main()
