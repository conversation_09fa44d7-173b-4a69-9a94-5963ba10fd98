/**
This file was auto-generated by mupdfwrap.py.
*/

#ifndef MUPDF_INTERNAL_H
#define MUPDF_INTERNAL_H


#include <iostream>


namespace mupdf
{

#define internal_assert(expression) (expression) ? (void) 0 : internal_assert_fail(__FILE__, __LINE__, __FUNCTION__, #expression)
FZ_FUNCTION void internal_assert_fail(const char* file, int line, const char* fn, const char* expression);

/** Internal use only. Looks at environmental variable <name>; returns 0 if unset else int value. */
FZ_FUNCTION int internal_env_flag(const char* name);

/** Internal use only. Looks at environmental variable <name>; returns 0 if unset else int value. */
FZ_FUNCTION int internal_env_flag_check_unset( const char* if_, const char* name);

/** Internal use only. Returns `fz_context*` for use by current thread. */
FZ_FUNCTION fz_context* internal_context_get();

} /* End of namespace mupdf. */

#endif
