/**
This file was auto-generated by mupdfwrap.py.
*/

#ifndef MUPDF_EXCEPTIONS_H
#define MUPDF_EXCEPTIONS_H


#include <stdexcept>
#include <string>

#include "mupdf/fitz.h"


namespace mupdf
{

/** Base class for exceptions. */
struct FzErrorBase : std::exception
{
	int         m_code;
	std::string m_text;
	mutable std::string m_what;
	FZ_FUNCTION const char* what() const throw();
	FZ_FUNCTION FzErrorBase(int code, const char* text);
};

/** For `FZ_ERROR_NONE`. */
struct FzErrorNone : FzErrorBase
{
	FZ_FUNCTION FzErrorNone(const char* message);
};


/** For `FZ_ERROR_GENERIC`. */
struct FzErrorGeneric : FzErrorBase
{
	FZ_FUNCTION FzErrorGeneric(const char* message);
};


/** For `FZ_ERROR_SYSTEM`. */
struct FzErrorSystem : FzErrorBase
{
	FZ_FUNCTION FzErrorSystem(const char* message);
};


/** For `FZ_ERROR_LIBRARY`. */
struct FzErrorLibrary : FzErrorBase
{
	FZ_FUNCTION FzErrorLibrary(const char* message);
};


/** For `FZ_ERROR_ARGUMENT`. */
struct FzErrorArgument : FzErrorBase
{
	FZ_FUNCTION FzErrorArgument(const char* message);
};


/** For `FZ_ERROR_LIMIT`. */
struct FzErrorLimit : FzErrorBase
{
	FZ_FUNCTION FzErrorLimit(const char* message);
};


/** For `FZ_ERROR_UNSUPPORTED`. */
struct FzErrorUnsupported : FzErrorBase
{
	FZ_FUNCTION FzErrorUnsupported(const char* message);
};


/** For `FZ_ERROR_FORMAT`. */
struct FzErrorFormat : FzErrorBase
{
	FZ_FUNCTION FzErrorFormat(const char* message);
};


/** For `FZ_ERROR_SYNTAX`. */
struct FzErrorSyntax : FzErrorBase
{
	FZ_FUNCTION FzErrorSyntax(const char* message);
};


/** For `FZ_ERROR_TRYLATER`. */
struct FzErrorTrylater : FzErrorBase
{
	FZ_FUNCTION FzErrorTrylater(const char* message);
};


/** For `FZ_ERROR_ABORT`. */
struct FzErrorAbort : FzErrorBase
{
	FZ_FUNCTION FzErrorAbort(const char* message);
};


/** For `FZ_ERROR_REPAIRED`. */
struct FzErrorRepaired : FzErrorBase
{
	FZ_FUNCTION FzErrorRepaired(const char* message);
};


/** Throw exception appropriate for error in `ctx`. */
FZ_FUNCTION void internal_throw_exception(fz_context* ctx);


} /* End of namespace mupdf. */

#endif
