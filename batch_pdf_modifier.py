#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量PDF文本修改工具
用于批量替换PDF文件中的多个文本字段
"""

import fitz  # PyMuPDF
import json
import os
from datetime import datetime
from typing import Dict, List


class BatchPDFModifier:
    def __init__(self, pdf_path: str):
        """
        初始化批量PDF修改器
        
        Args:
            pdf_path: PDF文件路径
        """
        self.pdf_path = pdf_path
        self.doc = None
        
    def open_pdf(self):
        """打开PDF文件"""
        try:
            self.doc = fitz.open(self.pdf_path)
            print(f"成功打开PDF文件: {self.pdf_path}")
            return True
        except Exception as e:
            print(f"打开PDF文件失败: {e}")
            return False
    
    def close_pdf(self):
        """关闭PDF文件"""
        if self.doc:
            self.doc.close()
    
    def batch_replace_text(self, replacements: Dict[str, str]) -> int:
        """
        批量替换文本
        
        Args:
            replacements: 替换字典，键为原文本，值为新文本
            
        Returns:
            替换的总数量
        """
        if not self.doc:
            print("请先打开PDF文件")
            return 0
        
        total_replaced = 0
        
        for old_text, new_text in replacements.items():
            replaced_count = 0
            
            for page_num in range(len(self.doc)):
                page = self.doc[page_num]
                
                # 查找要替换的文本
                text_instances = page.search_for(old_text)
                
                for rect in text_instances:
                    # 删除原文本区域
                    page.add_redact_annot(rect)
                    page.apply_redactions()
                    
                    # 在相同位置插入新文本
                    page.insert_text(rect.tl, new_text, fontsize=12, color=(0, 0, 0))
                    replaced_count += 1
            
            if replaced_count > 0:
                print(f"替换 '{old_text}' -> '{new_text}': {replaced_count} 处")
                total_replaced += replaced_count
            else:
                print(f"未找到文本: '{old_text}'")
        
        return total_replaced
    
    def save_pdf(self, output_path: str):
        """保存修改后的PDF"""
        if not self.doc:
            print("请先打开PDF文件")
            return False
        
        try:
            self.doc.save(output_path)
            print(f"PDF已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存PDF失败: {e}")
            return False


def load_replacement_config(config_file: str) -> Dict[str, str]:
    """
    从配置文件加载替换规则
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        替换字典
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"配置文件不存在: {config_file}")
        return {}
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return {}


def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "name": "张三",
        "email": "<EMAIL>",
        "Jun 19, 2025": "2025年6月19日",
        "$20.00": "¥150.00",
        "Cursor Pro": "Cursor专业版",
        "2F55C079՞0002": "INV-2025-001"
    }
    
    config_file = "replacement_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print(f"示例配置文件已创建: {config_file}")
    print("请编辑此文件来设置您的替换规则")
    return config_file


def main():
    """主函数"""
    pdf_file = "template_cursor.pdf"
    config_file = "replacement_config.json"
    
    if not os.path.exists(pdf_file):
        print(f"PDF文件不存在: {pdf_file}")
        return
    
    # 如果配置文件不存在，创建示例配置
    if not os.path.exists(config_file):
        print("配置文件不存在，正在创建示例配置...")
        create_sample_config()
        print("\n请编辑 replacement_config.json 文件，然后重新运行此脚本")
        return
    
    # 加载替换配置
    replacements = load_replacement_config(config_file)
    if not replacements:
        print("没有找到有效的替换配置")
        return
    
    print("将要执行的替换操作:")
    for old_text, new_text in replacements.items():
        print(f"  '{old_text}' -> '{new_text}'")
    
    # 确认执行
    confirm = input("\n确认执行批量替换? (y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 创建PDF修改器实例
    modifier = BatchPDFModifier(pdf_file)
    
    # 打开PDF文件
    if not modifier.open_pdf():
        return
    
    try:
        # 执行批量替换
        print("\n正在执行批量替换...")
        total_replaced = modifier.batch_replace_text(replacements)
        
        if total_replaced > 0:
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"modified_{timestamp}_{os.path.basename(pdf_file)}"
            
            # 保存修改后的文件
            modifier.save_pdf(output_file)
            print(f"\n批量替换完成！总共替换了 {total_replaced} 处文本")
            print(f"修改后的PDF已保存为: {output_file}")
        else:
            print("没有找到任何需要替换的文本")
    
    finally:
        # 关闭PDF文件
        modifier.close_pdf()


if __name__ == "__main__":
    main()
